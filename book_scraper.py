#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Books.toscrape.com 爬虫 - 加入Selenium版本
目标网站：http://books.toscrape.com/
学习目标：理解基础爬虫原理，学习Selenium基本用法
"""

import requests          # HTTP请求库 - 用于获取网页内容
from bs4 import BeautifulSoup  # HTML解析库 - 用于解析网页结构
import pandas as pd      # 数据处理库 - 用于数据组织和Excel保存
import time             # 时间库 - 用于延时操作
import random           # 随机数库 - 用于随机延时，避免被检测
import re               # 正则表达式库 - 用于文本匹配和提取
import os               # 操作系统接口 - 用于文件操作
from urllib.parse import urljoin, urlparse  # URL处理库 - 用于链接拼接
from datetime import datetime  # 日期时间库 - 用于时间戳
import logging          # 日志库 - 用于记录操作日志

# Selenium相关导入
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

class BookScraper:
    """图书爬虫类 - 封装所有爬虫功能"""
    
    def __init__(self, use_selenium=True):
        
        self.base_url = "http://books.toscrape.com/"  # 目标网站的根URL
        self.use_selenium = use_selenium  # 选择使用Selenium还是requests
        
        # 数据存储列表 - 用于存储爬取到的所有图书数据
        self.books_data = []
        
        # 初始化爬虫工具
        if self.use_selenium:
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            # chrome_options.add_argument('--headless')  # 取消注释可无头模式运行
            
            # 创建WebDriver
            self.driver = webdriver.Chrome(options=chrome_options)

        else:
            # 创建Session对象 - 用于保持连接，提高效率
            self.session = requests.Session()
            
            # 请求头设置 - 模拟真实浏览器访问
            self.headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }
            self.session.headers.update(self.headers)
        
        # 将英文星级转换为数字
        self.rating_map = {
            'One': 1, 'Two': 2, 'Three': 3, 'Four': 4, 'Five': 5
        }
    
    def get_page(self, url, max_retries=3):
        """
        获取页面内容 - 支持Selenium和requests两种方式
        
        参数:
            url: 要访问的网页地址
            max_retries: 最大重试次数（默认3次）
        """
        
        if self.use_selenium:
            # 使用Selenium获取页面
            self.driver.get(url)
            
            # 等待页面加载完成
            time.sleep(random.uniform(2, 4))
            
            # 返回页面源码
            return self.driver.page_source
        else:
            # 使用requests获取页面
            for attempt in range(max_retries):
                response = self.session.get(url, timeout=10)
                time.sleep(random.uniform(1, 3))
                return response
        
        return None
    
    def parse_book_details(self, book_element, page_url):
        """
        从单个图书元素中提取详细信息
        
        参数:
            book_element: BeautifulSoup解析的图书元素
            page_url: 当前页面的URL
            
        返回:
            dict: 包含图书信息的字典 
        """
        
        book_data = {}
        
        # 获取书名
        title_element = book_element.find('h3').find('a')
        book_data['书名'] = title_element.get('title', '').strip()
        
        # 获取详情页链接
        detail_link = title_element.get('href', '')
        book_data['详情链接'] = urljoin(page_url, detail_link)
        
        # 获取价格信息
        price_element = book_element.find('p', class_='price_color')
        price_text = price_element.text
        
        # 使用正则表达式提取数字部分
        price_match = re.search(r'[\d.]+', price_text)
        book_data['价格'] = float(price_match.group()) if price_match else 0.0
        book_data['价格文本'] = price_text  
        
        # 获取评分
        star_element = book_element.find('p', class_=re.compile(r'star-rating'))
        if star_element:
            star_class = star_element.get('class', [])
            for cls in star_class:
                if cls in self.rating_map:
                    book_data['评分'] = self.rating_map[cls]
                    break
            else:
                book_data['评分'] = 0
        else:
            book_data['评分'] = 0
        
        # 获取库存状态
        stock_element = book_element.find('p', class_='instock availability')
        stock_text = stock_element.text.strip()
        book_data['库存状态'] = stock_text
        book_data['有库存'] = 'in stock' in stock_text.lower()
        
        # 获取图片链接
        img_element = book_element.find('div', class_='image_container').find('img')
        img_src = img_element.get('src', '')
        book_data['图片链接'] = urljoin(page_url, img_src)
        
        return book_data
    
    def scrape_page(self, page_url):
        """
        爬取单个页面的所有书籍
        """
        
        # 获取页面内容
        page_content = self.get_page(page_url)
        if not page_content:
            return []
        
        # 处理不同方式获取的内容
        if self.use_selenium:
            # Selenium返回的是HTML字符串
            soup = BeautifulSoup(page_content, 'html.parser')
        else:
            # requests返回的是Response对象
            soup = BeautifulSoup(page_content.content, 'html.parser')
        
        # 查找所有书籍容器
        books = soup.find_all('article', class_='product_pod')
        print(f"在当前页面发现 {len(books)} 本书")
        
        # 存储当前页面的图书数据
        page_books = []
        
        # 遍历每本书
        for i, book in enumerate(books, 1):
            print(f"   正在解析第 {i}/{len(books)} 本书...")
            
            # 解析书籍详细信息
            book_data = self.parse_book_details(book, page_url)
            
            if book_data:
                page_books.append(book_data)
                
                # 显示进度信息
                title = book_data['书名'][:30] + "..." if len(book_data['书名']) > 30 else book_data['书名']
                print(f"    ✓ {title} - {book_data['价格文本']} - {book_data['评分']}星")
        
        print(f"页面解析完成，获取 {len(page_books)} 本书的信息")
        return page_books

    def get_total_pages(self):
        """
        获取网站总页数
        
        返回:
            int: 总页数
        """
        
        # 获取首页内容
        page_content = self.get_page(self.base_url)
        
        # 处理不同方式获取的内容
        if self.use_selenium:
            soup = BeautifulSoup(page_content, 'html.parser')
        else:
            soup = BeautifulSoup(page_content.content, 'html.parser')
        
        # 查找分页信息
        pager = soup.find('li', class_='current')
        
        if pager:
            text = pager.text.strip()
            match = re.search(r'Page \d+ of (\d+)', text)
            if match:
                total_pages = int(match.group(1))
                print(f"发现总共 {total_pages} 页")
                return total_pages
        
        print("未找到分页信息，默认为1页")
        return 1

    def scrape_all_books(self, max_pages=None):
        """
        爬取所有页面的书籍信息 - 主要工作流程
        
        参数:
            max_pages: 限制爬取的最大页数
        """
        
        print("开始爬取图书数据")
        print(f"使用方式: {'Selenium' if self.use_selenium else 'Requests'}")
        
        # 获取总页数
        total_pages = self.get_total_pages()
        
        # 限制页数
        if max_pages:
            total_pages = min(total_pages, max_pages)
            print(f"限制爬取 {total_pages} 页")
        
        # 逐页爬取
        for page_num in range(1, total_pages + 1):
            
            # 构造页面URL
            if page_num == 1:
                page_url = self.base_url
            else:
                page_url = f"{self.base_url}catalogue/page-{page_num}.html"
            
            print(f"正在爬取第 {page_num}/{total_pages} 页")
            print(f"URL: {page_url}")
            
            # 爬取当前页面
            page_books = self.scrape_page(page_url)
            
            # 将当前页面的数据添加到总数据中
            self.books_data.extend(page_books)
            
            # 显示进度
            print(f"第 {page_num} 页完成，新增 {len(page_books)} 本书")
            print(f"累计总数: {len(self.books_data)} 本书")
            
            # 页面间延时
            if page_num < total_pages:
                delay = random.uniform(2, 5)
                print(f"等待 {delay:.1f} 秒...")
                time.sleep(delay)
        
        print(f"爬取完成！总共获取 {len(self.books_data)} 本书的信息")
    
    def save_to_excel(self, filename=None):
        """
        保存数据到Excel文件
        
        参数:
            filename: 文件名（None时自动生成）
        """
        
        if not self.books_data:
            print("没有数据可保存")
            return
        
        # 生成文件名
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"books_data_{timestamp}.xlsx"
        
        # 创建DataFrame
        df = pd.DataFrame(self.books_data)
        
        # 计算统计信息
        total_books = len(df)
        avg_price = df['价格'].mean()
        avg_rating = df['评分'].mean()
        in_stock_count = df['有库存'].sum()
        
        # 保存Excel文件
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            
            # 主数据表
            df.to_excel(writer, sheet_name='图书数据', index=False)
            
            # 统计信息表
            stats_data = {
                '统计项目': ['总书籍数', '平均价格', '平均评分', '有库存数量', '库存率'],
                '数值': [
                    total_books, 
                    f'{avg_price:.2f}', 
                    f'{avg_rating:.1f}', 
                    in_stock_count, 
                    f'{in_stock_count/total_books*100:.1f}%'
                ]
            }
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
        
        print(f"数据已保存到: {filename}")
        print(f"统计信息: {total_books}本书, 平均价格£{avg_price:.2f}, 平均{avg_rating:.1f}星")
    
    def display_summary(self):
        """显示爬取结果摘要"""
        
        if not self.books_data:
            print("没有爬取到数据")
            return
        
        print("\n" + "="*60)
        print(f"爬取完成！共获取 {len(self.books_data)} 本书的信息")
        print("="*60)
        
        # 显示前5本书
        print("\n前5本书预览:")
        for i, book in enumerate(self.books_data[:5], 1):
            print(f"{i}. {book['书名']}")
            print(f"  价格: {book['价格文本']} | 评分: {book['评分']}星 | 库存: {'✓' if book['有库存'] else '✗'}")
        
        # 价格统计
        prices = [book['价格'] for book in self.books_data]
        print(f"\n价格统计:")
        print(f"   平均价格: £{sum(prices)/len(prices):.2f}")
        print(f"   最高价格: £{max(prices):.2f}")
        print(f"   最低价格: £{min(prices):.2f}")
        
        # 评分统计
        ratings = [book['评分'] for book in self.books_data if book['评分'] > 0]
        if ratings:
            print(f"\n评分统计:")
            print(f"   平均评分: {sum(ratings)/len(ratings):.1f}星")
            for star in range(1, 6):
                count = ratings.count(star)
                percentage = count/len(ratings)*100 if ratings else 0
                print(f"   {star}星: {count}本 ({percentage:.1f}%)")
    
    def close(self):
        """关闭WebDriver"""
        if self.use_selenium and hasattr(self, 'driver'):
            self.driver.quit()
            print("WebDriver 已关闭")

def main():
    """主函数"""
    
    # 选择使用方式
    use_method = input("选择爬取方式 (1: Selenium, 2: Requests, 默认: Selenium): ").strip()
    use_selenium = True if use_method != "2" else False
    
    max_pages = input("请输入要爬取的页数 (直接回车爬取所有页面): ").strip()
    max_pages = int(max_pages) if max_pages.isdigit() else None   
    
    # 创建爬虫实例
    scraper = BookScraper(use_selenium=use_selenium)
    
    # 开始计时
    start_time = time.time()
    
    # 开始爬取
    scraper.scrape_all_books(max_pages)
    
    # 结束计时
    end_time = time.time()
    
    # 显示结果
    scraper.display_summary()
    
    # 保存数据
    scraper.save_to_excel()
    
    print(f"\n总耗时: {end_time - start_time:.1f} 秒")
    print(f"数据文件已保存在当前目录")
    
    # 关闭WebDriver
    scraper.close()

if __name__ == "__main__":
    main()