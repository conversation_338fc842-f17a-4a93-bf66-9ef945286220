# Python程序设计综合训练 - 科学计算实验（改进版）
# 包含：Excel数据可视化、非线性方程组求解、最小二乘拟合、特征值计算、迷宫求解

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
from scipy import optimize, linalg, sparse
from scipy.sparse import csgraph
from scipy.optimize import fsolve, leastsq
from scipy.linalg import svd
import cv2
from PIL import Image
import seaborn as sns
from collections import deque
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

def load_and_visualize_excel_data():
    """加载并可视化Excel图书数据 - 每个图表单独生成"""
    print("1. Excel数据可视化")
    print("-" * 30)
    
    try:
        # 读取Excel文件
        df_books = pd.read_excel('books_data_20250629_170314.xlsx', sheet_name='图书数据')
        df_stats = pd.read_excel('books_data_20250629_170314.xlsx', sheet_name='统计信息')
        
        print(f"成功加载数据：{len(df_books)}本图书")
        
        # 图表1：价格分布直方图
        plt.figure(figsize=(10, 6))
        plt.hist(df_books['价格'], bins=15, color='skyblue', alpha=0.7, edgecolor='black')
        plt.title('图书价格分布', fontsize=14, fontweight='bold')
        plt.xlabel('价格 (£)')
        plt.ylabel('书籍数量')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
        
        # 图表2：评分分布饼图
        plt.figure(figsize=(8, 8))
        rating_counts = df_books['评分'].value_counts().sort_index()
        plt.pie(rating_counts.values, labels=[f'{i}星' for i in rating_counts.index], 
                autopct='%1.1f%%', startangle=90, colors=plt.cm.Set3.colors)
        plt.title('评分分布', fontsize=14, fontweight='bold')
        plt.tight_layout()
        plt.show()
        
        # 图表3：各评分等级平均价格
        plt.figure(figsize=(10, 6))
        avg_price_by_rating = df_books.groupby('评分')['价格'].mean()
        bars = plt.bar(avg_price_by_rating.index, avg_price_by_rating.values, 
                      color='lightcoral', alpha=0.7, edgecolor='black')
        plt.title('各评分等级平均价格', fontsize=14, fontweight='bold')
        plt.xlabel('评分')
        plt.ylabel('平均价格 (£)')
        plt.grid(True, alpha=0.3, axis='y')
        
        # 在柱状图上显示数值
        for bar, value in zip(bars, avg_price_by_rating.values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{value:.2f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.show()
        
        return df_books, df_stats
        
    except FileNotFoundError:
        print("Excel文件未找到，生成示例数据进行演示...")
        # 生成示例数据
        np.random.seed(42)
        n_books = 100
        df_books = pd.DataFrame({
            '价格': np.random.exponential(15, n_books) + 5,
            '评分': np.random.choice([1, 2, 3, 4, 5], n_books, p=[0.05, 0.1, 0.2, 0.4, 0.25])
        })
        
        # 重新绘制图表
        return load_and_visualize_excel_data()

def nonlinear_equations_demo():
    """非线性方程组求解演示"""
    print("\n2. 非线性方程组求解")
    print("-" * 30)
    
    # 定义方程组: 5*x1 + 3 = 0, 4*x0^2 - 2*sin(x1*x2) = 0, x1*x2 - 1.5 = 0
    def equations(x):
        x0, x1, x2 = x
        return [
            5*x1 + 3,
            4*x0**2 - 2*np.sin(x1*x2),
            x1*x2 - 1.5
        ]
    
    # 求解
    initial_guess = [1, 1, 1]
    solution = fsolve(equations, initial_guess)
    
    print(f"方程组解: x0={solution[0]:.6f}, x1={solution[1]:.6f}, x2={solution[2]:.6f}")
    print(f"验证误差: {equations(solution)}")
    
    return solution

def least_squares_fitting_demo():
    """最小二乘拟合演示"""
    print("\n3. 最小二乘拟合 - 正弦波拟合")
    print("-" * 30)
    
    # 生成带噪声的正弦波数据
    x = np.linspace(0, 2*np.pi, 100)
    true_params = [10, 0.34, np.pi/6]  # A, k, theta
    y_true = true_params[0] * np.sin(2*np.pi*true_params[1]*x + true_params[2])
    
    # 添加噪声
    np.random.seed(42)
    noise = 2 * np.random.randn(len(x))
    y_noisy = y_true + noise
    
    # 定义拟合函数和误差函数
    def sine_func(x, params):
        A, k, theta = params
        return A * np.sin(2*np.pi*k*x + theta)
    
    def residuals(params, x, y):
        return y - sine_func(x, params)
    
    # 执行拟合
    initial_params = [7, 0.40, 0]
    fitted_params, _ = leastsq(residuals, initial_params, args=(x, y_noisy))
    
    print(f"真实参数: A={true_params[0]}, k={true_params[1]:.3f}, θ={true_params[2]:.3f}")
    print(f"拟合参数: A={fitted_params[0]:.3f}, k={fitted_params[1]:.3f}, θ={fitted_params[2]:.3f}")
    
    # 可视化结果
    plt.figure(figsize=(12, 8))
    plt.subplot(2, 1, 1)
    plt.plot(x, y_noisy, 'o', alpha=0.6, label='带噪声的实验数据', markersize=4)
    plt.plot(x, y_true, 'g-', label='真实数据', linewidth=2)
    plt.plot(x, sine_func(x, fitted_params), 'r-', label='拟合数据', linewidth=2)
    plt.legend()
    plt.title('正弦波最小二乘拟合')
    plt.grid(True, alpha=0.3)
    
    # 残差分析
    plt.subplot(2, 1, 2)
    residual_values = residuals(fitted_params, x, y_noisy)
    plt.plot(x, residual_values, 'b-', alpha=0.7)
    plt.axhline(y=0, color='r', linestyle='--', alpha=0.7)
    plt.title('拟合残差')
    plt.xlabel('x')
    plt.ylabel('残差')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return fitted_params

def eigenvalue_demo():
    """特征值与特征向量演示"""
    print("\n4. 特征值与特征向量计算")
    print("-" * 30)
    
    # 创建示例矩阵
    A = np.array([[1, -0.3], [-0.1, 0.9]])
    
    # 计算特征值和特征向量
    eigenvalues, eigenvectors = linalg.eig(A)
    
    print(f"矩阵A:\n{A}")
    print(f"特征值: {eigenvalues}")
    print(f"特征向量:\n{eigenvectors}")
    
    # 可视化特征向量
    plt.figure(figsize=(10, 8))
    
    # 创建测试向量
    test_vectors = np.array([[1, 0], [1, 1], [0, 1]]).T
    colors = ['blue', 'green', 'red']
    
    # 绘制原始向量和变换后的向量
    for i, (vec, color) in enumerate(zip(test_vectors.T, colors)):
        # 原始向量
        plt.arrow(0, 0, vec[0], vec[1], head_width=0.05, head_length=0.05, 
                 fc=color, ec=color, alpha=0.7, label=f'原向量{i+1}')
        
        # 变换后的向量
        transformed = A @ vec
        plt.arrow(0, 0, transformed[0], transformed[1], head_width=0.05, head_length=0.05,
                 fc=color, ec=color, linestyle='--', alpha=0.7, label=f'变换后{i+1}')
    
    # 绘制特征向量
    for i, (val, vec) in enumerate(zip(eigenvalues, eigenvectors.T)):
        # 特征向量
        plt.arrow(0, 0, vec[0], vec[1], head_width=0.05, head_length=0.05,
                 fc='black', ec='black', linewidth=2, label=f'特征向量{i+1} (λ={val:.3f})')
        
        # 变换后的特征向量（应该与原向量方向相同，只是长度变化）
        transformed_eigen = A @ vec
        plt.arrow(0, 0, transformed_eigen[0], transformed_eigen[1], head_width=0.05, head_length=0.05,
                 fc='orange', ec='orange', linewidth=2, linestyle=':', 
                 label=f'变换后特征向量{i+1}')
    
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.title('线性变换中的特征向量')
    plt.xlabel('x')
    plt.ylabel('y')
    plt.tight_layout()
    plt.show()
    
    return eigenvalues, eigenvectors

def svd_image_compression_demo():
    """SVD图像压缩演示"""
    print("\n5. SVD图像压缩演示")
    print("-" * 30)
    
    # 创建或加载示例图像
    try:
        # 尝试加载图像文件
        img = cv2.imread('test_image.jpg', 0)  # 灰度模式
        if img is None:
            raise FileNotFoundError
    except:
        # 创建示例图像
        print("创建示例图像进行SVD压缩演示...")
        x = np.linspace(-5, 5, 200)
        y = np.linspace(-5, 5, 200)
        X, Y = np.meshgrid(x, y)
        img = 255 * np.exp(-(X**2 + Y**2)/4) * np.sin(X) * np.cos(Y)
        img = np.abs(img).astype(np.uint8)
    
    # 对图像进行SVD分解
    U, s, Vt = svd(img)
    
    # 不同奇异值数量的重构
    ranks = [5, 20, 50, 100]
    
    plt.figure(figsize=(15, 10))
    
    # 原始图像
    plt.subplot(2, 3, 1)
    plt.imshow(img, cmap='gray')
    plt.title(f'原始图像 ({img.shape[0]}×{img.shape[1]})')
    plt.axis('off')
    
    # 奇异值分布
    plt.subplot(2, 3, 2)
    plt.semilogy(s[:100], 'b-', linewidth=2)
    plt.title('前100个奇异值')
    plt.xlabel('索引')
    plt.ylabel('奇异值（对数尺度）')
    plt.grid(True, alpha=0.3)
    
    # 不同压缩比的重构图像
    for i, rank in enumerate(ranks):
        plt.subplot(2, 3, i+3)
        
        # 重构图像
        img_reconstructed = U[:, :rank] @ np.diag(s[:rank]) @ Vt[:rank, :]
        img_reconstructed = np.clip(img_reconstructed, 0, 255).astype(np.uint8)
        
        plt.imshow(img_reconstructed, cmap='gray')
        
        # 计算压缩比和误差
        original_size = img.size
        compressed_size = rank * (U.shape[0] + Vt.shape[1] + 1)
        compression_ratio = compressed_size / original_size
        mse = np.mean((img.astype(float) - img_reconstructed.astype(float))**2)
        
        plt.title(f'秩{rank}重构\n压缩比:{compression_ratio:.2f}, MSE:{mse:.1f}')
        plt.axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return U, s, Vt

def create_sample_maze(size=50):
    """创建示例迷宫"""
    maze = np.ones((size, size), dtype=np.uint8) * 255  # 白色背景
    
    # 创建一些墙壁（黑色）
    for i in range(0, size, 3):
        maze[i, :] = 0
        maze[:, i] = 0
    
    # 创建一些通道
    for i in range(1, size-1, 6):
        for j in range(1, size-1, 6):
            maze[i:i+2, j:j+2] = 255
    
    # 确保边界有入口和出口
    maze[0, 1] = 255  # 顶部入口
    maze[size-1, size-2] = 255  # 底部出口
    
    return maze

def shortest_path_demo():
    """最短路径演示"""
    print("\n6. 最短路径算法演示")
    print("-" * 30)
    
    # 创建图的邻接矩阵
    n_nodes = 6
    graph = np.array([
        [0, 4, 2, 0, 0, 0],
        [4, 0, 1, 5, 0, 0],
        [2, 1, 0, 8, 10, 0],
        [0, 5, 8, 0, 2, 6],
        [0, 0, 10, 2, 0, 3],
        [0, 0, 0, 6, 3, 0]
    ])
    
    # 将0替换为无穷大（表示无连接）
    graph_inf = graph.astype(float)
    graph_inf[graph_inf == 0] = np.inf
    np.fill_diagonal(graph_inf, 0)
    
    # 计算所有节点间的最短路径
    distances, predecessors = csgraph.dijkstra(graph_inf, return_predecessors=True)
    
    print("邻接矩阵:")
    print(graph)
    print("\n所有节点间最短距离:")
    print(distances)
    
    # 可视化图和最短路径
    plt.figure(figsize=(12, 8))
    
    # 节点位置
    pos = {
        0: (0, 1),
        1: (1, 2),
        2: (1, 0),
        3: (2, 2),
        4: (2, 0),
        5: (3, 1)
    }
    
    # 绘制边
    for i in range(n_nodes):
        for j in range(i+1, n_nodes):
            if graph[i, j] > 0:
                x1, y1 = pos[i]
                x2, y2 = pos[j]
                plt.plot([x1, x2], [y1, y2], 'b-', alpha=0.6, linewidth=1)
                # 标注边权重
                mid_x, mid_y = (x1 + x2) / 2, (y1 + y2) / 2
                plt.text(mid_x, mid_y, str(graph[i, j]), fontsize=10, 
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
    
    # 绘制节点
    for node, (x, y) in pos.items():
        plt.scatter(x, y, s=500, c='red', alpha=0.8)
        plt.text(x, y, str(node), fontsize=12, ha='center', va='center', color='white', fontweight='bold')
    
    # 高亮从节点0到节点5的最短路径
    path = []
    current = 5
    while current != 0:
        path.append(current)
        current = predecessors[0, current]
    path.append(0)
    path.reverse()
    
    for i in range(len(path)-1):
        x1, y1 = pos[path[i]]
        x2, y2 = pos[path[i+1]]
        plt.plot([x1, x2], [y1, y2], 'r-', linewidth=4, alpha=0.7)
    
    plt.title(f'最短路径演示\n从节点0到节点5的最短路径: {" → ".join(map(str, path))}\n距离: {distances[0, 5]:.0f}')
    plt.axis('equal')
    plt.grid(True, alpha=0.3)
    plt.show()
    
    return distances, predecessors

class AdvancedMazeSolver:
    """改进的迷宫求解器，能够处理变色墙壁"""
    
    def __init__(self, image_path=None, image_array=None):
        self.image_path = image_path
        self.image_array = image_array
        self.original = None
        self.binary_maze = None
        self.start = None
        self.end = None
        
    def load_image(self):
        """加载迷宫图像"""
        if self.image_array is not None:
            self.original = self.image_array
        else:
            self.original = cv2.imread(self.image_path)
        
        if self.original is None:
            raise ValueError("无法加载迷宫图像")
        
        return self.original
    
    def intelligent_preprocess_maze(self, method='kmeans'):
        """
        智能预处理迷宫图像，能够处理变色墙壁
        """
        print(f"使用{method}方法智能预处理迷宫图像...")
        
        # 转换为灰度图
        if len(self.original.shape) == 3:
            gray = cv2.cvtColor(self.original, cv2.COLOR_BGR2GRAY)
        else:
            gray = self.original.copy()
        
        if method == 'kmeans':
            # 使用K-means聚类区分路径和墙壁
            h, w = gray.shape
            data = gray.reshape(-1, 1).astype(np.float32)
            
            # K-means聚类，分为2类
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 100, 0.2)
            _, labels, centers = cv2.kmeans(data, 2, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
            
            # 重塑标签
            labels = labels.reshape(h, w)
            
            # 确定哪个聚类是路径（通常是较亮的）
            if centers[0] > centers[1]:
                path_label = 0
            else:
                path_label = 1
            
            # 创建二值掩码
            self.binary_maze = (labels == path_label).astype(int)
            
        elif method == 'adaptive':
            # 自适应阈值
            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                         cv2.THRESH_BINARY, 21, 2)
            self.binary_maze = (binary > 128).astype(int)
            
        elif method == 'otsu':
            # Otsu自动阈值
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            self.binary_maze = (binary > 128).astype(int)
        
        # 形态学操作，清理噪声
        kernel = np.ones((3,3), np.uint8)
        binary_clean = cv2.morphologyEx(self.binary_maze.astype(np.uint8), cv2.MORPH_CLOSE, kernel)
        binary_clean = cv2.morphologyEx(binary_clean, cv2.MORPH_OPEN, kernel)
        self.binary_maze = binary_clean.astype(int)
        
        print(f"迷宫尺寸: {self.binary_maze.shape}")
        print(f"可通行像素数: {np.sum(self.binary_maze)}")
        print(f"可通行区域比例: {np.sum(self.binary_maze) / self.binary_maze.size * 100:.1f}%")
        
        return self.binary_maze
    
    def find_start_end_points(self):
        """自动寻找起点和终点"""
        height, width = self.binary_maze.shape
        
        # 寻找边界上的可通行点
        boundary_points = []
        
        # 检查所有边界
        edges = [
            [(0, x) for x in range(width) if self.binary_maze[0, x] == 1],  # 上边界
            [(height-1, x) for x in range(width) if self.binary_maze[height-1, x] == 1],  # 下边界
            [(y, 0) for y in range(height) if self.binary_maze[y, 0] == 1],  # 左边界
            [(y, width-1) for y in range(height) if self.binary_maze[y, width-1] == 1]  # 右边界
        ]
        
        for edge in edges:
            boundary_points.extend(edge)
        
        if len(boundary_points) >= 2:
            # 选择距离最远的两个点
            max_dist = 0
            best_pair = None
            for i in range(len(boundary_points)):
                for j in range(i+1, len(boundary_points)):
                    p1, p2 = boundary_points[i], boundary_points[j]
                    dist = np.sqrt((p1[0]-p2[0])**2 + (p1[1]-p2[1])**2)
                    if dist > max_dist:
                        max_dist = dist
                        best_pair = (p1, p2)
            
            self.start, self.end = best_pair
        else:
            # fallback：在可通行区域中找到合适的起点和终点
            path_coords = np.where(self.binary_maze == 1)
            if len(path_coords[0]) > 0:
                # 选择最上方和最下方的可通行点
                top_idx = np.argmin(path_coords[0])
                bottom_idx = np.argmax(path_coords[0])
                self.start = (path_coords[0][top_idx], path_coords[1][top_idx])
                self.end = (path_coords[0][bottom_idx], path_coords[1][bottom_idx])
            else:
                raise ValueError("迷宫中没有找到可通行的路径")
        
        print(f"起点: {self.start}, 终点: {self.end}")
        return self.start, self.end
    
    def bfs_solve(self):
        """使用广度优先搜索求解迷宫"""
        print("使用BFS算法求解迷宫...")
        
        if self.start is None or self.end is None:
            self.find_start_end_points()
        
        height, width = self.binary_maze.shape
        queue = deque([(self.start, [self.start])])
        visited = set([self.start])
        
        # 四个方向：上下左右
        directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]
        
        nodes_explored = 0
        while queue:
            (y, x), path = queue.popleft()
            nodes_explored += 1
            
            if (y, x) == self.end:
                print(f"找到路径！路径长度: {len(path)}")
                print(f"探索节点数: {nodes_explored}")
                return path
            
            for dy, dx in directions:
                ny, nx = y + dy, x + dx
                
                if (0 <= ny < height and 0 <= nx < width and 
                    self.binary_maze[ny, nx] == 1 and (ny, nx) not in visited):
                    
                    visited.add((ny, nx))
                    queue.append(((ny, nx), path + [(ny, nx)]))
        
        print("未找到路径！")
        return None
    
    def visualize_preprocessing(self, methods=['kmeans', 'adaptive', 'otsu']):
        """可视化不同预处理方法的效果"""
        plt.figure(figsize=(15, 5))
        
        # 原始图像
        plt.subplot(1, len(methods)+1, 1)
        plt.imshow(self.original, cmap='gray')
        plt.title('原始迷宫')
        plt.axis('off')
        
        # 不同方法的处理结果
        for i, method in enumerate(methods):
            plt.subplot(1, len(methods)+1, i+2)
            
            # 保存当前的binary_maze
            original_binary = self.binary_maze.copy() if self.binary_maze is not None else None
            
            # 应用不同方法
            self.intelligent_preprocess_maze(method)
            
            plt.imshow(self.binary_maze, cmap='gray')
            plt.title(f'{method.upper()}方法\n可通行:{np.sum(self.binary_maze)}像素')
            plt.axis('off')
            
            # 恢复原始binary_maze
            if original_binary is not None:
                self.binary_maze = original_binary
        
        plt.tight_layout()
        plt.show()
    
    def visualize_solution(self, path, method_name="BFS"):
        """可视化迷宫和解决方案"""
        if path is None:
            print("没有路径可以可视化")
            return
        
        plt.figure(figsize=(15, 10))
        
        # 原始迷宫
        plt.subplot(2, 2, 1)
        plt.imshow(self.original, cmap='gray')
        plt.title('原始迷宫图像')
        plt.axis('off')
        
        # 预处理后的迷宫
        plt.subplot(2, 2, 2)
        plt.imshow(self.binary_maze, cmap='gray')
        plt.title('预处理后的迷宫\n(白色=路径, 黑色=墙壁)')
        plt.axis('off')
        
        # 求解结果
        plt.subplot(2, 2, 3)
        display_maze = np.zeros((*self.binary_maze.shape, 3))
        display_maze[self.binary_maze == 1] = [1, 1, 1]  # 白色路径
        display_maze[self.binary_maze == 0] = [0, 0, 0]  # 黑色墙壁
        
        # 绘制路径
        if path:
            for y, x in path:
                if 0 <= y < self.binary_maze.shape[0] and 0 <= x < self.binary_maze.shape[1]:
                    display_maze[y, x] = [1, 0, 0]  # 红色路径
        
        # 标记起点和终点
        if self.start:
            display_maze[self.start[0], self.start[1]] = [0, 1, 0]  # 绿色起点
        if self.end:
            display_maze[self.end[0], self.end[1]] = [0, 0, 1]  # 蓝色终点
        
        plt.imshow(display_maze)
        plt.title(f'求解结果 ({method_name})\n绿色=起点, 蓝色=终点, 红色=路径')
        plt.axis('off')
        
        # 路径统计
        plt.subplot(2, 2, 4)
        if path:
            # 绘制路径长度随步数的变化
            path_lengths = list(range(1, len(path)+1))
            plt.plot(path_lengths, 'b-', linewidth=2)
            plt.title(f'路径统计\n总长度: {len(path)} 步')
            plt.xlabel('步数')
            plt.ylabel('累计长度')
            plt.grid(True, alpha=0.3)
        else:
            plt.text(0.5, 0.5, '未找到路径', ha='center', va='center', fontsize=20)
            plt.axis('off')
        
        plt.tight_layout()
        plt.show()

def solve_uploaded_maze(image_path, method='kmeans'):
    """处理用户上传的迷宫图像（改进版）"""
    print(f"\n处理上传的迷宫图像: {image_path}")
    print(f"使用预处理方法: {method}")
    print("-" * 50)
    
    try:
        # 创建改进的迷宫求解器
        solver = AdvancedMazeSolver(image_path)
        
        # 加载图像
        original = solver.load_image()
        print(f"成功加载图像，尺寸: {original.shape}")
        
        # 比较不同预处理方法
        print("比较不同预处理方法...")
        solver.visualize_preprocessing()
        
        # 使用指定方法预处理
        binary_maze = solver.intelligent_preprocess_maze(method)
        
        # 寻找起点和终点
        start, end = solver.find_start_end_points()
        
        # 求解迷宫
        path = solver.bfs_solve()
        
        # 可视化结果
        solver.visualize_solution(path, f"BFS + {method.upper()}")
        
        if path:
            print(f"成功找到路径！")
            print(f"路径长度: {len(path)} 步")
            print(f"起点: {start}")
            print(f"终点: {end}")
        else:
            print("未找到路径，可能需要尝试其他预处理方法")
        
        return solver, path
        
    except Exception as e:
        print(f"处理迷宫图像时出错: {e}")
        print("建议检查：")
        print("1. 图像文件是否存在")
        print("2. 图像格式是否支持")
        print("3. 迷宫是否有明确的路径结构")
        return None, None

def main():
    """主程序"""
    print("Python科学计算综合实验")
    print("=" * 60)
    
    # 1. Excel数据可视化
    try:
        df_books, df_stats = load_and_visualize_excel_data()
    except Exception as e:
        print(f"Excel数据可视化出错: {e}")
    
    # 2. 非线性方程组求解
    try:
        solution = nonlinear_equations_demo()
    except Exception as e:
        print(f"非线性方程组求解出错: {e}")
    
    # 3. 最小二乘拟合
    try:
        fitted_params = least_squares_fitting_demo()
    except Exception as e:
        print(f"最小二乘拟合出错: {e}")
    
    # 4. 特征值与特征向量
    try:
        eigenvals, eigenvecs = eigenvalue_demo()
    except Exception as e:
        print(f"特征值计算出错: {e}")
    
    # 5. SVD图像压缩
    try:
        U, s, Vt = svd_image_compression_demo()
    except Exception as e:
        print(f"SVD图像压缩出错: {e}")
    
    # 6. 最短路径
    try:
        distances, predecessors = shortest_path_demo()
    except Exception as e:
        print(f"最短路径演示出错: {e}")
    
    # 7. 迷宫求解
    print("\n7. 迷宫求解演示")
    print("-" * 30)
    
    try:
        # 首先尝试加载用户的迷宫图像
        # 用户需要将迷宫图像文件名改为 "maze.png" 或修改下面的文件名
        maze_file = "maze.png"  # 修改为你的迷宫图像文件名
        
        solver, path = solve_uploaded_maze(maze_file, method='kmeans')
        
        if solver is None:
            print("使用示例迷宫进行演示...")
            # 创建示例迷宫
            sample_maze = create_sample_maze(100)
            solver = AdvancedMazeSolver(image_array=sample_maze)
            solver.load_image()
            solver.intelligent_preprocess_maze('adaptive')
            path = solver.bfs_solve()
            solver.visualize_solution(path, "示例迷宫")
            
    except Exception as e:
        print(f"迷宫求解出现错误: {e}")
    
    print("\n" + "=" * 60)
    print("实验完成！")
    print("=" * 60)

# 使用说明
print("""
改进功能说明:
1. 每个可视化图表现在单独生成，便于查看
2. 改进的迷宫识别算法，支持多种预处理方法：
   - K-means聚类：适用于变色墙壁
   - 自适应阈值：适用于光照不均
   - Otsu自动阈值：适用于双峰分布
3. 增加了SVD图像压缩演示
4. 更好的错误处理和调试信息

使用方法:
1. 运行 main() 执行完整实验
2. 运行 solve_uploaded_maze("你的迷宫图像.png", method='kmeans') 处理特定迷宫
3. 可选的预处理方法: 'kmeans', 'adaptive', 'otsu'

示例:
solver, path = solve_uploaded_maze("maze.png", method='kmeans')
""")

if __name__ == "__main__":
    main()