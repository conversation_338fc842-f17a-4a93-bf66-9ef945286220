
"""
办公自动化综合练习
包括：词云图生成、图片处理、Word文档自动化

"""

import re                    # 正则表达式库
import jieba                 # 中文分词库
from openpyxl import load_workbook  # Excel读写库
from wordcloud import WordCloud     # 词云库
import matplotlib.pyplot as plt     # 绘图库
import cv2                   # 图片处理库
import numpy as np           # 数值计算库
from docx import Document    # Word文档处理库
from docx.shared import Inches      # Word文档尺寸单位
import os                    # 文件操作库

class OfficeAutomation:
    """办公自动化类"""
    
    def __init__(self):  
        # 停用词列表 - 过滤无意义词汇
        self.stopwords = [
            '的', '和', '是', '在', '了', '有', '个', '与', '或', '等', '及', '为',
            '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', 'the', 'a',
            'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with'
        ]
    
    def read_excel_data(self, filename='books_data_20250629_170314.xlsx'):
        """
        1. 读取Excel数据
        参数:
            filename: Excel文件名
        返回:
            list: 数据列表
        """ 
        # 加载Excel工作簿
        workbook = load_workbook(filename)
        
        # 获取第一个工作表
        worksheet = workbook.active
        
        # 读取所有数据
        data = []
        for row in range(2, worksheet.max_row + 1):  # 从第2行开始（跳过标题）
            book_name = worksheet[f'A{row}'].value  # 书名列
            if book_name:
                data.append(book_name)
        
        return data
    
    def clean_text(self, text_list):
        """
        2. 数据清洗 - 去除HTML标签和特殊字符
        
        参数:
            text_list: 文本列表
            
        返回:
            str: 清洗后的文本
        """
        # 合并所有文本
        combined_text = ' '.join(text_list)
        
        # 去除HTML标签 - 使用正则表达式
        clean_text = re.sub(r'<.*?>', '', combined_text)
        
        # 去除特殊字符，只保留中文、英文、数字和空格
        clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', clean_text)
      
        return clean_text
    

    
    def filter_stopwords(self, word_list):
        """
        4. 过滤停用词
        
        参数:
            word_list: 分词结果列表
            
        返回:
            str: 过滤后的文本
        """
        word_list = jieba.lcut(word_list)
      
      # 过滤停用词和长度小于2的词
        filtered_words = []
        for word in word_list:
            if word not in self.stopwords and len(word.strip()) >= 2:
                filtered_words.append(word)
        
        # 重新组合文本
        result_text = ' '.join(filtered_words)
        
        return result_text
    
    def generate_wordcloud(self, text, font_path=None):
        """
        5. 生成词云图
        
        参数:
            text: 处理后的文本
            font_path: 字体文件路径（可选）
        """
        
        wc = WordCloud(
            width=1600,
            height=1200,
            max_words=100,
            background_color='white')
            
        # 生成词云
        wordcloud = wc.generate(text)
        
        # 保存词云图
        wordcloud.to_file('词云图.png')
        
        # 显示词云图
        plt.figure(figsize=(16, 12))
        plt.imshow(wordcloud, interpolation='bilinear')
        plt.axis('off')
        plt.title('图书词云图', fontsize=20)
        plt.tight_layout()
        plt.savefig('词云图_显示.png', dpi=300, bbox_inches='tight')
        plt.show()
        

    def get_word_frequency(self, text):
        """
        6. 获取词频统计
        
        参数:
            text: 处理后的文本
        """
        # 重新分词
        words = text.split()
        
        # 统计词频
        word_count = {}
        for word in words:
            word_count[word] = word_count.get(word, 0) + 1
        
        # 按频率排序
        sorted_words = sorted(word_count.items(), key=lambda x: x[1], reverse=True)
        
        # 显示前10个高频词
        print("\n前10个高频词:")
        for i, (word, count) in enumerate(sorted_words[:10], 1):
            print(f"{i}. {word}: {count}次")
        
        return sorted_words
    
    def create_signature(self, signature_path='signature.jpg'):
        """
        7. 图片处理 - 制作电子签名
        
        参数:
            signature_path: 签名图片路径
        """
        print("正在处理电子签名...")
        

        # 读取签名图片
        img = cv2.imread(signature_path)
        
        # 转为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 二值化处理 - 将图片转为黑白
        ret, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        
        # 转为BGRA格式（带透明通道）
        bgra = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGRA)
        
        # 设置透明度 - 白色部分设为透明
        for i in range(bgra.shape[0]):
            for j in range(bgra.shape[1]):
                if bgra[i, j][0] == 255:  # 白色像素
                    bgra[i, j][3] = 0  # 设为透明
        
        # 保存处理后的签名
        cv2.imwrite('signature_processed.png', bgra)
        
        print("电子签名处理完成，已保存为 'signature_processed.png'")
        return 'signature_processed.png'
    
    def create_word_document(self, data_list, signature_path=None):
        """
        8. 自动化处理Word文档
        
        参数:
            data_list: 数据列表
            signature_path: 电子签名路径
        """
        
        # 创建新文档
        doc = Document()
        
        # 添加标题
        title = doc.add_heading('图书信息报告', 0)
        title.alignment = 1  # 居中对齐
        
        # 添加段落
        doc.add_paragraph('本报告基于Excel数据自动生成，展示了图书信息的统计分析。')
        
        # 添加图书列表
        doc.add_heading('图书清单', level=1)
        
        # 创建表格
        table = doc.add_table(rows=1, cols=2)
        table.style = 'Table Grid'
        
        # 设置表头
        header_cells = table.rows[0].cells
        header_cells[0].text = '序号'
        header_cells[1].text = '图书名称'
        
        # 添加数据行（限制前10本书）
        for i, book_name in enumerate(data_list[:10], 1):
            row_cells = table.add_row().cells
            row_cells[0].text = str(i)
            row_cells[1].text = book_name
        
        # 添加签名部分
        doc.add_heading('报告签名', level=1)
        
        # 创建签名表格
        sig_table = doc.add_table(rows=1, cols=2)
        sig_cells = sig_table.rows[0].cells
        sig_cells[0].text = '制作人：'
        
        # 如果有电子签名，插入图片
       
        paragraph = sig_cells[1].paragraphs[0]
        run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
        run.add_picture(signature_path, width=Inches(1), height=Inches(0.5))

        
        # 保存文档
        filename = '图书信息报告.docx'
        doc.save(filename)
        
        print(f"Word文档生成完成，已保存为 '{filename}'")
    
    def run_automation(self):

        # 1. 读取Excel数据
        data = self.read_excel_data()
        
        # 2. 数据清洗
        clean_text = self.clean_text(data)
        
        
        #   3. 过滤停用词
        filtered_text = self.filter_stopwords(clean_text)
        
        # 5.生成词云图
        self.generate_wordcloud(filtered_text)
        
        # 6. 获取词频统计
        word_freq = self.get_word_frequency(filtered_text)
        
        # 7. 处理电子签名
        signature_path = self.create_signature()
        
        # 8. 生成Word文档
        self.create_word_document(data, signature_path)
        

            



if __name__ == "__main__":
    
    automation = OfficeAutomation()
    automation.run_automation()