# 基于Adaptive自适应阈值的迷宫求解器
import numpy as np
import matplotlib.pyplot as plt
import cv2
from collections import deque
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class AdaptiveMazeSolver:
    def __init__(self, image_path):
        self.image_path = image_path
        self.original_image = None
        self.binary_maze = None
        self.start = None
        self.end = None
        
    def load_image(self):
        """加载迷宫图像"""
        try:
            self.original_image = cv2.imread(self.image_path, cv2.IMREAD_GRAYSCALE)
            if self.original_image is None:
                raise FileNotFoundError
            print(f"成功加载图像，尺寸: {self.original_image.shape}")
            return True
        except:
            print("无法加载图像文件")
            return False
    
    def adaptive_binarization(self, block_size=21, C=2):
        """使用自适应阈值进行二值化"""
        print("使用Adaptive自适应阈值进行二值化...")
        
        # 自适应阈值二值化
        adaptive_binary = cv2.adaptiveThreshold(
            self.original_image, 255, 
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 
            block_size, C
        )
        
        # 转换为0-1矩阵，1表示可通行（白色区域）
        self.binary_maze = (adaptive_binary > 128).astype(int)
        
        # 轻微的形态学处理，清理小噪声但保持细通道
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(self.binary_maze.astype(np.uint8), cv2.MORPH_CLOSE, kernel)
        
        # 去除极小的噪声点
        kernel_small = np.ones((1, 1), np.uint8)
        final_binary = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel_small)
        
        self.binary_maze = final_binary.astype(int)
        
        print(f"自适应二值化完成，可通行像素: {np.sum(self.binary_maze)}")
        print(f"可通行比例: {np.sum(self.binary_maze) / self.binary_maze.size * 100:.2f}%")
        
        return self.binary_maze
    
    def find_start_end_points(self):
        """寻找起点和终点"""
        print("寻找起点和终点...")
        
        height, width = self.binary_maze.shape
        
        # 寻找边界上的可通行点
        boundary_points = []
        
        # 检查四个边界，找到所有可通行的边界点
        for x in range(width):
            if self.binary_maze[0, x] == 1:  # 上边界
                boundary_points.append(('top', (0, x)))
            if self.binary_maze[height-1, x] == 1:  # 下边界
                boundary_points.append(('bottom', (height-1, x)))
        
        for y in range(height):
            if self.binary_maze[y, 0] == 1:  # 左边界
                boundary_points.append(('left', (y, 0)))
            if self.binary_maze[y, width-1] == 1:  # 右边界
                boundary_points.append(('right', (y, width-1)))
        
        # 分类边界点
        top_points = [p[1] for p in boundary_points if p[0] == 'top']
        bottom_points = [p[1] for p in boundary_points if p[0] == 'bottom']
        left_points = [p[1] for p in boundary_points if p[0] == 'left']
        right_points = [p[1] for p in boundary_points if p[0] == 'right']
        
        # 根据迷宫图像，START在底部，END在顶部
        if bottom_points and top_points:
            # 选择最中央的点
            center_x = width // 2
            self.start = min(bottom_points, key=lambda p: abs(p[1] - center_x))  # START在底部
            self.end = min(top_points, key=lambda p: abs(p[1] - center_x))      # END在顶部
        elif len(boundary_points) >= 2:
            # 如果没有上下边界点，选择距离最远的两个点
            all_points = [p[1] for p in boundary_points]
            max_dist = 0
            best_pair = None
            for i in range(len(all_points)):
                for j in range(i+1, len(all_points)):
                    p1, p2 = all_points[i], all_points[j]
                    dist = (p1[0]-p2[0])**2 + (p1[1]-p2[1])**2
                    if dist > max_dist:
                        max_dist = dist
                        best_pair = (p1, p2)
            
            if best_pair:
                # 确保start是下方的点，end是上方的点
                p1, p2 = best_pair
                if p1[0] > p2[0]:  # p1更靠下
                    self.start, self.end = p1, p2
                else:
                    self.start, self.end = p2, p1
        else:
            # 备用方案：在内部找点
            path_coords = np.where(self.binary_maze == 1)
            if len(path_coords[0]) > 0:
                # 最下方点作为起点
                bottom_idx = np.argmax(path_coords[0])
                self.start = (path_coords[0][bottom_idx], path_coords[1][bottom_idx])
                # 最上方点作为终点
                top_idx = np.argmin(path_coords[0])
                self.end = (path_coords[0][top_idx], path_coords[1][top_idx])
        
        # 验证起点终点是否在可通行区域
        if self.start and self.binary_maze[self.start[0], self.start[1]] != 1:
            self.start = self.find_nearest_passable_point(self.start)
            
        if self.end and self.binary_maze[self.end[0], self.end[1]] != 1:
            self.end = self.find_nearest_passable_point(self.end)
        
        print(f"起点和终点已确定")
        return self.start, self.end
    
    def find_nearest_passable_point(self, point):
        """在给定点附近寻找最近的可通行点"""
        y, x = point
        height, width = self.binary_maze.shape
        
        # 在点周围搜索可通行的点
        for radius in range(1, 20):  # 搜索半径逐渐增大
            for dy in range(-radius, radius+1):
                for dx in range(-radius, radius+1):
                    ny, nx = y + dy, x + dx
                    if (0 <= ny < height and 0 <= nx < width and 
                        self.binary_maze[ny, nx] == 1):
                        return (ny, nx)
        
        # 如果没找到，返回原点
        return point
    
    def check_connectivity(self):
        """检查起点和终点是否连通"""
        if not self.start or not self.end:
            return False, "起点或终点未设置"
        
        print("检查起点和终点的连通性...")
        
        # 使用简单的BFS检查连通性
        visited = set()
        queue = deque([self.start])
        visited.add(self.start)
        
        directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]
        height, width = self.binary_maze.shape
        
        step_count = 0
        max_steps = 10000  # 限制搜索步数
        
        while queue and step_count < max_steps:
            current = queue.popleft()
            step_count += 1
            
            if current == self.end:
                print(f"✅ 起点和终点连通！搜索了 {step_count} 步")
                return True, "连通"
            
            y, x = current
            for dy, dx in directions:
                ny, nx = y + dy, x + dx
                if (0 <= ny < height and 0 <= nx < width and 
                    (ny, nx) not in visited and 
                    self.binary_maze[ny, nx] == 1):
                    visited.add((ny, nx))
                    queue.append((ny, nx))
        
        print(f"❌ 起点和终点不连通！搜索了 {step_count} 步")
        return False, "不连通"
        """验证路径是否有效（不穿墙）"""
        if not path:
            return False, "路径为空"
        
        invalid_points = []
        for i, (y, x) in enumerate(path):
            if self.binary_maze[y, x] != 1:
                invalid_points.append((i, y, x))
        
        if invalid_points:
            return False, f"路径中有 {len(invalid_points)} 个点穿墙"
        
        return True, "路径有效，无穿墙"
    
    def bfs_pathfinding(self):
        """使用BFS算法寻找路径"""
        print("正在搜索路径...")
        
        if self.start is None or self.end is None:
            print("起点或终点未设置，无法进行路径搜索")
            return None
        
        height, width = self.binary_maze.shape
        queue = deque([(self.start, [self.start])])
        visited = set([self.start])
        
        # 四个方向移动
        directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]
        
        explored_count = 0
        max_iterations = min(height * width, 200000)
        
        while queue and explored_count < max_iterations:
            (y, x), path = queue.popleft()
            explored_count += 1
            
            # 每探索10000个节点报告一次进度
            if explored_count % 10000 == 0:
                print(f"搜索进度: {explored_count} 个节点")
            
            # 到达终点
            if (y, x) == self.end:
                print(f"找到路径！长度: {len(path)} 步")
                
                # 验证路径
                is_valid, message = self.validate_path(path)
                
                return path if is_valid else None
            
            # 探索四个方向
            for dy, dx in directions:
                ny, nx = y + dy, x + dx
                
                # 检查边界和可通行性
                if (0 <= ny < height and 0 <= nx < width and 
                    (ny, nx) not in visited and 
                    self.binary_maze[ny, nx] == 1):
                    
                    visited.add((ny, nx))
                    queue.append(((ny, nx), path + [(ny, nx)]))
        
        print("未找到路径")
        return None
    
    def visualize_result(self, path):
        """可视化结果"""
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        # 原始图像
        axes[0].imshow(self.original_image, cmap='gray')
        axes[0].set_title('原始迷宫', fontsize=14)
        axes[0].axis('off')
        
        # 自适应二值化结果
        axes[1].imshow(self.binary_maze, cmap='gray')
        axes[1].set_title('Adaptive二值化结果', fontsize=14)
        axes[1].axis('off')
        
        # 路径结果
        if path:
            # 在原图上绘制路径
            result_img = cv2.cvtColor(self.original_image, cv2.COLOR_GRAY2RGB)
            
            # 绘制路径（红色，粗线）
            for i in range(len(path)-1):
                y1, x1 = path[i]
                y2, x2 = path[i+1]
                cv2.line(result_img, (x1, y1), (x2, y2), (255, 0, 0), thickness=3)
            
            axes[2].imshow(result_img)
            axes[2].set_title(f'迷宫解决方案\n路径长度: {len(path)} 步', fontsize=14)
        else:
            axes[2].imshow(self.original_image, cmap='gray')
            axes[2].set_title('未找到路径', fontsize=14)
        
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.show()
    
    def save_final_result(self, path, output_filename="adaptive_maze_solution.png"):
        """保存最终结果"""
        if path:
            print(f"保存最终结果到: {output_filename}")
            
            # 创建结果图像
            result_img = cv2.cvtColor(self.original_image, cv2.COLOR_GRAY2RGB)
            
            # 绘制路径（红色，粗线）
            for i in range(len(path)-1):
                y1, x1 = path[i]
                y2, x2 = path[i+1]
                cv2.line(result_img, (x1, y1), (x2, y2), (255, 0, 0), thickness=4)
            
            # 显示和保存
            plt.figure(figsize=(14, 14))
            plt.imshow(result_img)
            plt.title(f'迷宫解决方案\n路径长度: {len(path)} 步', fontsize=18, fontweight='bold')
            plt.axis('off')
            
            plt.savefig(output_filename, dpi=300, bbox_inches='tight', pad_inches=0.1)
            plt.show()
            
            print(f"结果已保存为: {output_filename}")
            return True
        else:
            print("没有有效路径可以保存")
            return False

def solve_maze_adaptive(image_path, block_size=21, C=2):
    """使用Adaptive方法的迷宫求解主函数"""
    print("=" * 60)
    print("基于Adaptive自适应阈值的迷宫求解器")
    print("=" * 60)
    
    solver = AdaptiveMazeSolver(image_path)
    
    # 加载图像
    if not solver.load_image():
        return None, None
    
    # 使用自适应阈值二值化
    solver.adaptive_binarization(block_size=block_size, C=C)
    
    # 寻找起点和终点
    solver.find_start_end_points()
    
    # 检查连通性
    is_connected, conn_message = solver.check_connectivity()
    if not is_connected:
        print(f"⚠️  连通性检查失败: {conn_message}")
        print("可能的原因:")
        print("1. 迷宫确实无解")
        print("2. 起点或终点识别错误")
        print("3. 二值化参数需要调整")
        
        # 显示当前结果但继续尝试
        solver.visualize_result(None)
        return solver, None
    
    # 寻找路径
    path = solver.bfs_pathfinding()
    
    # 显示结果
    solver.visualize_result(path)
    
    # 保存最终结果
    if path:
        solver.save_final_result(path, "adaptive_迷宫解决方案.png")
        
        print(f"\n✅ 迷宫求解成功！")
        print(f"📍 起点: {solver.start}")
        print(f"🎯 终点: {solver.end}")
        print(f"📏 路径长度: {len(path)} 步")
        print(f"📐 图像尺寸: {solver.original_image.shape}")
        
        # 显示路径片段
        print(f"\n🛣️  路径示例（前5步和后5步）:")
        for i, (y, x) in enumerate(path):
            if i < 5:
                print(f"   第{i+1}步: ({y}, {x})")
            elif i == 5 and len(path) > 10:
                print(f"   ... (中间省略 {len(path)-10} 步) ...")
            elif i >= len(path) - 5:
                print(f"   第{i+1}步: ({y}, {x})")
        
        # 路径质量检查
        is_valid, message = solver.validate_path(path)
        print(f"✔️  路径质量: {message}")
        
    else:
        print("\n❌ 未能找到有效路径")
        print("💡 建议尝试调整参数:")
        print(f"   - block_size (当前: {block_size}, 可试: 15, 25, 31)")
        print(f"   - C (当前: {C}, 可试: 1, 3, 5)")
    
    print("=" * 60)
    
    return solver, path

# 使用示例
if __name__ == "__main__":
    # 使用adaptive方法求解迷宫
    solver, path = solve_maze_adaptive("maze.png")
    
    # 如果效果不理想，可以调整参数再试
    # solver, path = solve_maze_adaptive("maze.png", block_size=15, C=3)
    # solver, path = solve_maze_adaptive("maze.png", block_size=31, C=1)
    
    print("\n🔧 Adaptive方法参数说明:")
    print("- block_size: 自适应阈值的邻域大小（奇数，通常15-31）")
    print("- C: 从均值中减去的常数（通常1-5）")
    print("- 如果路径断开，减小C值；如果噪声太多，增大C值")